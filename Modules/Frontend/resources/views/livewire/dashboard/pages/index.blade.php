@extends('adminlte::page')

@section('title', 'Dashboard')

@section('content_header')
@stop

@section('content')

    <div class="mb-5">
        <div class="border-0">
            <div class="py-4">
                <h1 class="h3 text-dark mb-1"><PERSON><PERSON>, {{ auth()->user()->name }}</h1>
                <p class="text-muted mb-0">{{ ucfirst(strftime('%A, %d de %B del %Y', strtotime('today'))) }}</p>
                @php setlocale(LC_TIME, 'es_ES.UTF-8'); @endphp
            </div>
        </div>
    </div>
    <!-- actualizacion de datos importante -->
    @if (DB::table('apertura_procesos')->whereDate('fecha_desde', '<=', Carbon\Carbon::now()->format('Y-m-d'))->whereDate('fecha_hasta', '>=', Carbon\Carbon::now()->format('Y-m-d'))->where('tipo', 2)->where('codigo_v', 'ASOCIADOS_UPDATE_DATA')->count() &&
            DB::table('asociados_update_data')->where('idasociado', auth()->user()->asociado_idasociado)->count() == 0)
        <livewire:frontend::dashboard.components.asociados.pages.index :asociado="auth()->user()->asociado" />
    @else
        <!-- fin actualizacion de datos importante -->
        <!-- Estadísticas principales -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 h-100">
                    <div class="card-body text-center py-4">
                        <h3 class="h4 text-dark mb-1">{{ number_format($datosFinancieros['totalAhorrado'], 2) }}</h3>
                        <p class="text-muted mb-0 small">Total ahorrado (Bs.)</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 h-100">
                    <div class="card-body text-center py-4">
                        <h3 class="h4 text-dark mb-1">{{ $datosFinancieros['prestamosActivos'] }}</h3>
                        <p class="text-muted mb-0 small">Préstamos activos</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 h-100">
                    <div class="card-body text-center py-4">
                        <h3 class="h4 text-dark mb-1">{{ number_format($datosFinancieros['disponiblePrestamos'], 2) }}</h3>
                        <p class="text-muted mb-0 small">Disponible para préstamos (Bs.)</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 h-100">
                    <div class="card-body text-center py-4">
                        <h3 class="h4 text-dark mb-1">{{ $grupoFamiliar }}</h3>
                        <p class="text-muted mb-0 small">Miembros del grupo familiar</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">

            @if (auth()->user()->asociado->condicion_soc == 9 ||
                    auth()->user()->asociado->contrato->where('estatus', 3)->count() > 0)
                <div class="col-12">
                    <x-adminlte-alert theme="danger" title="Estimado(a) asociado">

                        Queremos informarte que tu condición de asociado se encuentra en revisión. Esto es un
                        proceso normal que nos ayuda a asegurarnos de que tengas acceso a todos los beneficios y
                        servicios que ofrecemos. Por favor, comunícate con nosotros para regularizar tu situación.
                        Recuerda que es importante mantener tu información personal y laboral actualizada para evitar
                        inconvenientes. Si necesitas ayuda o tienes alguna pregunta, no dudes en contactarnos. <br> ¡Estamos
                        aquí
                        para ayudarte!.
                        <br>
                        Número de contacto: 0424-5311634 <br>[Departamento de Gestión de Pago]


                    </x-adminlte-alert>
                </div>
            @else
                <!-- Fila principal con enlaces rápidos, préstamos y actividad reciente -->
                <div class="row">
                    <!-- Enlaces rápidos y actividad reciente -->
                    <div class="mb-4 col-lg-4">
                        <!-- Acciones rápidas -->
                        <div class="mb-4">
                            <h5 class="text-dark mb-3">Acciones rápidas</h5>
                            <div class="list-group border-0">
                                <a wire:navigate class="list-group-item list-group-item-action border-0 py-3 px-0"
                                    href="{{ route('dashboard.estado-de-cuenta.index') }}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-dark">Estado de cuenta</span>
                                        <i class="fas fa-chevron-right text-muted"></i>
                                    </div>
                                </a>
                                <a wire:navigate class="list-group-item list-group-item-action border-0 py-3 px-0"
                                    href="{{ route('dashboard.gestion-de-pago.index') }}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-dark">Gestión de pago</span>
                                        <i class="fas fa-chevron-right text-muted"></i>
                                    </div>
                                </a>
                                <a wire:navigate class="list-group-item list-group-item-action border-0 py-3 px-0"
                                    href="{{ route('dashboard.solicitudes.index') }}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-dark">Solicitar financiamiento</span>
                                        <i class="fas fa-chevron-right text-muted"></i>
                                    </div>
                                </a>
                                <div class="list-group-item border-0 py-3 px-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-muted">Retiro de ahorros</span>
                                        <span class="text-muted small">Próximamente</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Solicitudes recientes -->
                        <div class="mb-4">
                            <h5 class="text-dark mb-3">Solicitudes recientes</h5>
                            @if(count($actividadReciente['solicitudes']) > 0)
                                @foreach($actividadReciente['solicitudes']->take(3) as $solicitud)
                                    <div class="d-flex justify-content-between align-items-center py-3 border-bottom">
                                        <div>
                                            <p class="mb-0 text-dark">Solicitud de financiamiento</p>
                                            <small class="text-muted">{{ Carbon\Carbon::parse($solicitud->fecha_sol)->diffForHumans() }}</small>
                                        </div>
                                        <span class="text-dark font-weight-bold">{{ number_format($solicitud->montot_prest, 2) }} Bs.</span>
                                    </div>
                                @endforeach
                                <div class="pt-3">
                                    <a wire:navigate href="{{ route('dashboard.solicitudes.index') }}" class="text-primary small">
                                        Ver todas las solicitudes →
                                    </a>
                                </div>
                            @else
                                <p class="text-muted">No hay solicitudes recientes</p>
                            @endif
                        </div>
                    </div>

                    <!-- Préstamos y notificaciones -->
                    <div class="mb-4 col-lg-8">
                        <!-- Notificaciones -->
                        <livewire:frontend::dashboard.components.notificaciones.procesos lazy />

                        <!-- Mis préstamos -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="text-dark mb-0">Mis préstamos</h5>
                                <a wire:navigate href="{{ route('dashboard.prestamos.index') }}" class="text-primary small">
                                    Ver todos →
                                </a>
                            </div>
                            @if ($prestamos->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-borderless">
                                        <tbody>
                                            @foreach ($prestamos as $prestamo)
                                                <tr>
                                                    <td class="py-3 border-bottom">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div>
                                                                <p class="mb-0 text-dark font-weight-bold">{{ $prestamo['controlDePrestamo']->descripcion }}</p>
                                                                <small class="text-muted">{{ date('d/m/Y', strtotime($prestamo->fecha_prest)) }}</small>
                                                            </div>
                                                            <div class="text-right">
                                                                <p class="mb-0 text-dark font-weight-bold">{{ number_format($prestamo->montot_prest, 2) }} Bs.</p>
                                                                @if ($prestamo->condic_prest == 1)
                                                                    <small class="text-success">Activo</small>
                                                                @else
                                                                    <small class="text-muted">Inactivo</small>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                @if ($prestamos->count() > 4)
                                    <div class="text-center pt-3">
                                        {{ $prestamos->links() }}
                                    </div>
                                @endif
                            @else
                                <div class="text-center py-5">
                                    <p class="text-muted mb-3">No tienes préstamos activos</p>
                                    <a wire:navigate href="{{ route('dashboard.solicitudes.prestamos.index') }}" class="btn btn-outline-primary btn-sm">
                                        Solicitar préstamo
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif
        </div>
    @endif

@stop
