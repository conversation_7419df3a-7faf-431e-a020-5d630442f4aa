@extends('adminlte::page')

@section('title', 'Dashboard')

@section('content_header')
@stop

@section('adminlte_css')
<style>
    /* <PERSON><PERSON><PERSON> visuales avanzadas */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        --info-gradient: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
        --warning-gradient: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
        --card-shadow: 0 2px 15px rgba(0,0,0,0.08);
        --card-shadow-hover: 0 8px 25px rgba(0,0,0,0.15);
    }

    .card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 12px;
        border: none;
        box-shadow: var(--card-shadow);
    }

    .card:hover {
        box-shadow: var(--card-shadow-hover);
        transform: translateY(-2px);
    }

    .stats-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-left: 5px solid #007bff;
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        transform: translate(30px, -30px);
    }

    .stats-card.success {
        border-left-color: #28a745;
        background: linear-gradient(135deg, #ffffff 0%, #f8fff8 100%);
    }

    .stats-card.info {
        border-left-color: #17a2b8;
        background: linear-gradient(135deg, #ffffff 0%, #f0fdff 100%);
    }

    .stats-card.warning {
        border-left-color: #ffc107;
        background: linear-gradient(135deg, #ffffff 0%, #fffef0 100%);
    }

    .stats-card.primary {
        border-left-color: #007bff;
        background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        position: relative;
        z-index: 2;
    }

    .stats-icon.success {
        background: var(--success-gradient);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .stats-icon.info {
        background: var(--info-gradient);
        box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
    }

    .stats-icon.warning {
        background: var(--warning-gradient);
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    }

    .stats-icon.primary {
        background: var(--primary-gradient);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .list-group-item {
        transition: all 0.3s ease;
        border-radius: 8px !important;
        margin-bottom: 10px;
        border: 1px solid #e9ecef;
        position: relative;
        overflow: hidden;
    }

    .list-group-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 4px;
        background: transparent;
        transition: all 0.3s ease;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
        transform: translateX(8px);
        border-color: #007bff;
    }

    .list-group-item:hover::before {
        background: #007bff;
    }

    .welcome-section {
        background: var(--primary-gradient);
        color: white;
        border-radius: 15px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .welcome-section::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 200px;
        height: 200px;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .welcome-content {
        position: relative;
        z-index: 2;
    }

    .btn {
        transition: all 0.3s ease;
        border-radius: 8px;
        font-weight: 500;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .btn-outline-primary:hover {
        background: var(--primary-gradient);
        border-color: transparent;
    }

    .section-title {
        position: relative;
        padding-left: 15px;
        margin-bottom: 1.5rem;
    }

    .section-title::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: var(--primary-gradient);
        border-radius: 2px;
    }

    .loan-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 10px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .loan-card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 15px rgba(0,123,255,0.1);
    }

    .loan-status-active {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
    }

    .loan-status-inactive {
        background: linear-gradient(135deg, #6c757d, #adb5bd);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3px 10px rgba(108, 117, 125, 0.3);
    }

    .empty-state {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 2px dashed #dee2e6;
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .empty-state:hover {
        border-color: #007bff;
        background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
    }

    .pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .fade-in {
        animation: fadeIn 0.6s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@stop

@section('content')

    <div class="mb-4 fade-in">
        <div class="welcome-section p-4">
            <div class="welcome-content">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="h3 mb-2 font-weight-bold">¡Hola, {{ auth()->user()->name }}!</h1>
                        <p class="mb-0 opacity-75">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            {{ ucfirst(strftime('%A, %d de %B del %Y', strtotime('today'))) }}
                        </p>
                        @php setlocale(LC_TIME, 'es_ES.UTF-8'); @endphp
                    </div>
                    <div class="col-md-4 text-right d-none d-md-block">
                        <div class="pulse">
                            <i class="fas fa-user-circle fa-4x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- actualizacion de datos importante -->
    @if (DB::table('apertura_procesos')->whereDate('fecha_desde', '<=', Carbon\Carbon::now()->format('Y-m-d'))->whereDate('fecha_hasta', '>=', Carbon\Carbon::now()->format('Y-m-d'))->where('tipo', 2)->where('codigo_v', 'ASOCIADOS_UPDATE_DATA')->count() &&
            DB::table('asociados_update_data')->where('idasociado', auth()->user()->asociado_idasociado)->count() == 0)
        <livewire:frontend::dashboard.components.asociados.pages.index :asociado="auth()->user()->asociado" />
    @else
        <!-- fin actualizacion de datos importante -->
        <!-- Estadísticas principales -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3 fade-in" style="animation-delay: 0.1s;">
                <div class="card stats-card success h-100">
                    <div class="card-body text-center py-4">
                        <div class="stats-icon success">
                            <i class="fas fa-piggy-bank fa-lg text-white"></i>
                        </div>
                        <h3 class="h4 text-dark mb-1 font-weight-bold">{{ number_format($datosFinancieros['totalAhorrado'], 2) }}</h3>
                        <p class="text-muted mb-0 small font-weight-medium">Total ahorrado (Bs.)</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3 fade-in" style="animation-delay: 0.2s;">
                <div class="card stats-card info h-100">
                    <div class="card-body text-center py-4">
                        <div class="stats-icon info">
                            <i class="fas fa-hand-holding-usd fa-lg text-white"></i>
                        </div>
                        <h3 class="h4 text-dark mb-1 font-weight-bold">{{ $datosFinancieros['prestamosActivos'] }}</h3>
                        <p class="text-muted mb-0 small font-weight-medium">Préstamos activos</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3 fade-in" style="animation-delay: 0.3s;">
                <div class="card stats-card warning h-100">
                    <div class="card-body text-center py-4">
                        <div class="stats-icon warning">
                            <i class="fas fa-coins fa-lg text-white"></i>
                        </div>
                        <h3 class="h4 text-dark mb-1 font-weight-bold">{{ number_format($datosFinancieros['disponiblePrestamos'], 2) }}</h3>
                        <p class="text-muted mb-0 small font-weight-medium">Disponible para préstamos (Bs.)</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3 fade-in" style="animation-delay: 0.4s;">
                <div class="card stats-card primary h-100">
                    <div class="card-body text-center py-4">
                        <div class="stats-icon primary">
                            <i class="fas fa-users fa-lg text-white"></i>
                        </div>
                        <h3 class="h4 text-dark mb-1 font-weight-bold">{{ $grupoFamiliar }}</h3>
                        <p class="text-muted mb-0 small font-weight-medium">Miembros del grupo familiar</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Row -->
        <div class="rows">

            @if (auth()->user()->asociado->condicion_soc == 9 ||
                    auth()->user()->asociado->contrato->where('estatus', 3)->count() > 0)
                <div class="col-12">
                    <x-adminlte-alert theme="danger" title="Estimado(a) asociado">

                        Queremos informarte que tu condición de asociado se encuentra en revisión. Esto es un
                        proceso normal que nos ayuda a asegurarnos de que tengas acceso a todos los beneficios y
                        servicios que ofrecemos. Por favor, comunícate con nosotros para regularizar tu situación.
                        Recuerda que es importante mantener tu información personal y laboral actualizada para evitar
                        inconvenientes. Si necesitas ayuda o tienes alguna pregunta, no dudes en contactarnos. <br> ¡Estamos
                        aquí
                        para ayudarte!.
                        <br>
                        Número de contacto: 0424-5311634 <br>[Departamento de Gestión de Pago]


                    </x-adminlte-alert>
                </div>
            @else
                <!-- Fila principal con enlaces rápidos, préstamos y actividad reciente -->
                <div class="row">
                    <!-- Enlaces rápidos y actividad reciente -->
                    <div class="mb-4 col-lg-4">
                        <!-- Acciones rápidas -->
                        <div class="mb-4">
                            <h5 class="section-title text-dark">
                                <i class="fas fa-bolt text-primary mr-2"></i>
                                Acciones rápidas
                            </h5>
                            <div class="list-group border-0">
                                <a wire:navigate class="list-group-item list-group-item-action py-3 px-3"
                                    href="{{ route('dashboard.estado-de-cuenta.index') }}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="stats-icon primary mr-3" style="width: 40px; height: 40px; margin: 0;">
                                                <i class="fas fa-file-invoice-dollar text-white"></i>
                                            </div>
                                            <div>
                                                <span class="text-dark font-weight-medium">Estado de cuenta</span>
                                                <small class="text-muted d-block">Consulta tu historial</small>
                                            </div>
                                        </div>
                                        <i class="fas fa-chevron-right text-muted"></i>
                                    </div>
                                </a>
                                <a wire:navigate class="list-group-item list-group-item-action py-3 px-3"
                                    href="{{ route('dashboard.gestion-de-pago.index') }}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="stats-icon success mr-3" style="width: 40px; height: 40px; margin: 0;">
                                                <i class="fas fa-credit-card text-white"></i>
                                            </div>
                                            <div>
                                                <span class="text-dark font-weight-medium">Gestión de pago</span>
                                                <small class="text-muted d-block">Realiza pagos</small>
                                            </div>
                                        </div>
                                        <i class="fas fa-chevron-right text-muted"></i>
                                    </div>
                                </a>
                                <a wire:navigate class="list-group-item list-group-item-action py-3 px-3"
                                    href="{{ route('dashboard.solicitudes.index') }}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="stats-icon info mr-3" style="width: 40px; height: 40px; margin: 0;">
                                                <i class="fas fa-hand-holding-usd text-white"></i>
                                            </div>
                                            <div>
                                                <span class="text-dark font-weight-medium">Solicitar financiamiento</span>
                                                <small class="text-muted d-block">Préstamos y créditos</small>
                                            </div>
                                        </div>
                                        <i class="fas fa-chevron-right text-muted"></i>
                                    </div>
                                </a>
                                <div class="list-group-item py-3 px-3 bg-light opacity-75">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-secondary rounded-circle mr-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                <i class="fas fa-money-bill-wave text-white"></i>
                                            </div>
                                            <div>
                                                <span class="text-muted font-weight-medium">Retiro de ahorros</span>
                                                <small class="text-muted d-block">Próximamente</small>
                                            </div>
                                        </div>
                                        <span class="badge badge-secondary">Próximamente</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Solicitudes recientes -->
                        <div class="mb-4">
                            <h5 class="section-title text-dark">
                                <i class="fas fa-clock text-warning mr-2"></i>
                                Solicitudes recientes
                            </h5>
                            @if(count($actividadReciente['solicitudes']) > 0)
                                @foreach($actividadReciente['solicitudes']->take(3) as $solicitud)
                                    <div class="card mb-3 loan-card">
                                        <div class="card-body py-3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <div class="stats-icon warning mr-3" style="width: 35px; height: 35px; margin: 0;">
                                                        <i class="fas fa-file-alt text-white"></i>
                                                    </div>
                                                    <div>
                                                        <p class="mb-0 text-dark font-weight-medium">Solicitud de financiamiento</p>
                                                        <small class="text-muted">
                                                            <i class="fas fa-calendar-alt mr-1"></i>
                                                            {{ Carbon\Carbon::parse($solicitud->fecha_sol)->diffForHumans() }}
                                                        </small>
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-dark font-weight-bold h6">{{ number_format($solicitud->montot_prest, 2) }}</span>
                                                    <small class="text-muted d-block">Bs.</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                                <div class="text-center">
                                    <a wire:navigate href="{{ route('dashboard.solicitudes.index') }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye mr-1"></i>
                                        Ver todas las solicitudes
                                    </a>
                                </div>
                            @else
                                <div class="card empty-state">
                                    <div class="card-body text-center py-4">
                                        <i class="fas fa-inbox fa-2x text-muted mb-3 pulse"></i>
                                        <p class="text-muted mb-0">No hay solicitudes recientes</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Préstamos y notificaciones -->
                    <div class="mb-4 col-lg-8">
                        <!-- Notificaciones -->
                        <livewire:frontend::dashboard.components.notificaciones.procesos lazy />

                        <!-- Mis préstamos -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="section-title text-dark mb-0">
                                    <i class="fas fa-money-check-alt text-success mr-2"></i>
                                    Mis préstamos
                                </h5>
                                <a wire:navigate href="{{ route('dashboard.prestamos.index') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye mr-1"></i>
                                    Ver todos
                                </a>
                            </div>
                            @if ($prestamos->count() > 0)
                                <div class="row">
                                    @foreach ($prestamos as $prestamo)
                                        <div class="col-12 mb-3">
                                            <div class="card loan-card">
                                                <div class="card-body py-3">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div class="d-flex align-items-center">
                                                            @if ($prestamo->condic_prest == 1)
                                                                <div class="loan-status-active mr-3">
                                                                    <i class="fas fa-check"></i>
                                                                </div>
                                                            @else
                                                                <div class="loan-status-inactive mr-3">
                                                                    <i class="fas fa-pause"></i>
                                                                </div>
                                                            @endif
                                                            <div>
                                                                <p class="mb-1 text-dark font-weight-bold">{{ $prestamo['controlDePrestamo']->descripcion }}</p>
                                                                <small class="text-muted">
                                                                    <i class="fas fa-calendar-alt mr-1"></i>
                                                                    {{ date('d/m/Y', strtotime($prestamo->fecha_prest)) }}
                                                                </small>
                                                            </div>
                                                        </div>
                                                        <div class="text-right">
                                                            <p class="mb-1 text-dark font-weight-bold h5">{{ number_format($prestamo->montot_prest, 2) }}</p>
                                                            <small class="text-muted d-block">Bs.</small>
                                                            @if ($prestamo->condic_prest == 1)
                                                                <span class="badge badge-success">Activo</span>
                                                            @else
                                                                <span class="badge badge-secondary">Inactivo</span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                @if ($prestamos->count() > 4)
                                    <div class="text-center pt-3">
                                        {{ $prestamos->links() }}
                                    </div>
                                @endif
                            @else
                                <div class="card empty-state">
                                    <div class="card-body text-center py-5">
                                        <i class="fas fa-hand-holding-usd fa-3x text-muted mb-3 pulse"></i>
                                        <h6 class="text-muted mb-3">No tienes préstamos activos</h6>
                                        <p class="text-muted mb-4 small">Solicita tu primer préstamo y comienza a hacer realidad tus proyectos</p>
                                        <a wire:navigate href="{{ route('dashboard.solicitudes.prestamos.index') }}" class="btn btn-primary">
                                            <i class="fas fa-plus mr-2"></i>
                                            Solicitar préstamo
                                        </a>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif
        </div>
    @endif

@stop
